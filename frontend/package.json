{"name": "frontend", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service lint"}, "dependencies": {"axios": "^1.10.0", "core-js": "^3.8.3", "element-plus": "^2.8.6", "vue": "^3.2.13", "vue-router": "^4.4.5", "vxe-table": "^4.7.94", "xlsx": "^0.18.5"}, "devDependencies": {"@babel/core": "^7.12.16", "@babel/eslint-parser": "^7.12.16", "@vue/cli-plugin-babel": "~5.0.0", "@vue/cli-plugin-eslint": "~5.0.0", "@vue/cli-service": "~5.0.0", "autoprefixer": "^10.4.21", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "eslint": "^7.32.0", "eslint-plugin-vue": "^8.0.3", "lucide-vue-next": "^0.525.0", "postcss": "^8.5.6", "radix-vue": "^1.9.17", "shadcn-vue": "^2.2.0", "tailwind-merge": "^3.3.1", "tailwindcss": "^3.4.17"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/vue3-essential", "eslint:recommended"], "parserOptions": {"parser": "@babel/eslint-parser"}, "rules": {}}, "browserslist": ["> 1%", "last 2 versions", "not dead", "not ie 11"]}