import { createRouter, createWebHistory } from 'vue-router';
import ReportList from '../components/ReportList.vue';
import ReportInfo from '../components/ReportInfo.vue';

const routes = [
  {
    path: '/',
    name: 'ReportList',
    component: ReportList
  },
  { path: '/reportInfo/:id',
    name: 'ReportInfo',
    component: ReportInfo
  },


];

const router = createRouter({
  history: createWebHistory(),
  routes
});

export default router;
