<template>
  <div>

    <!-- 返回主页按钮 -->
    <div class="home-btn-container">
      <el-button type="text" icon="el-icon-arrow-left" @click="goHome">返回主页</el-button>
    </div>


    <h1 class="title">{{ chinese_domain_name }}实验报告详情：{{ experiment.name }}</h1>

    <!-- 选择类型的下拉框 -->
    <el-select v-model="selectedType" placeholder="选择类型" @change="fetchDataFrames" style="margin-bottom: 20px;" class="type-select">
      <el-option label="聚合" value="merged"></el-option>
      <el-option label="不聚合" value="not_merged"></el-option>
    </el-select>

    <!-- 业务场景下拉框 -->
    <el-select v-if="sceneOptions.length" v-model="selectedScene" placeholder="选择场景" @change="fetchDataFrames" style="margin-left: 20px;margin-bottom: 20px;" class="type-select">
      <el-option
        v-for="env in sceneOptions"
        :key="env"
        :label="env"
        :value="env"
      ></el-option>
    </el-select>
    


    <!-- 基础信息卡片 -->
    <el-card class="experiment-info-card" shadow="hover">
      <div class="experiment-info">
        <el-row :gutter="20" class="info-row">
          <el-col :span="12" class="left-col">
            <p><strong>实验名称：</strong>{{ experiment.name }}</p>
            <p><strong>实验ID：</strong>{{ experiment.experiment_id }}</p>
            <p><strong>流量大小：</strong>{{ experiment.user_size || '未提供' }}</p>
          </el-col>
          <el-col :span="12" class="right-col">
            <p><strong>实验周期：</strong>AA: {{ experiment.AA || '-' }} / AB: {{ experiment.AB }}</p>
            <p><strong>对照组ID：</strong>{{ experiment.control_id }}</p>
            <p><strong>备注：</strong>{{ experiment.notes || '无' }}</p>
          </el-col>
        </el-row>
      </div>
    </el-card>

  <!-- 导出按钮 -->
    <div class="export-btn-container">
      <el-button type="primary" @click="exportToExcel">导出数据</el-button>
    </div>

    <div v-for="(df, index) in dataFrames" :key="index" class="data-table-container">
    <h3 class="table-title">{{ df.name }}</h3>
    <h4 class="table-note">{{ df.note }}</h4>
    <el-table :data="df.data" border style="width: 100%" :span-method="(tableRow) => arraySpanMethod(tableRow, df)" class="custom-table">
      <el-table-column
        v-for="col in df.columns"
        :key="col"
        :prop="col"
        :label="col"
        align="center"
        :width="col === '指标' ? '150px' : 'auto'"
      >
        <template #default="scope">
          <span
            v-if="['AB平均增量', 'AB平均增幅','AA平均增量', 'AA平均增幅','AB-AA'].includes(col)"
            :style="{ color: String(scope.row[col]).trim().startsWith('-') ? 'green' : 'red' }"
          >
            {{ scope.row[col] }}
          </span>
          <span
            v-else-if="['曝光pv', '曝光uv','点击pv', '点击uv','pv点击率','uv点击率'].includes(col) &&  ['相对增长','绝对增长'].includes(scope.row.组别)"
            :style="{ color: String(scope.row[col]).trim().startsWith('-') ? 'green' : 'red' }"
          >
            {{ scope.row[col] }}
          </span>
          <span v-else>
            {{ scope.row[col] }}
          </span>
        </template>
      </el-table-column>
    </el-table>
    <div v-if="df.is_split" class="table-divider"></div>
  </div>
</div>

</template>

<script>
import axios from 'axios';
import { ref, onMounted } from 'vue';
// 引入 Element Plus 的 Loading 服务和消息组件
import { ElLoading, ElMessage } from 'element-plus';
import { useRoute, useRouter } from 'vue-router';
import * as XLSX from 'xlsx';  // 引入 xlsx 库


export default {
  setup() {
    const route = useRoute();
    const router = useRouter();
    const selectedType = ref('merged');
    const selectedScene = ref('overall');
    const sceneOptions = ref([]);
    const dataFrames = ref([]);
    const experiment = ref({
      name: '',
      AA: '',
      AB: '',
      experiment_id: '',
      control_id: '',
      user: '',
      status: '',
      notes: ''
    });
    const domain = ref(route.query.domain || 'browser');
    const chinese_domain_name = ref('');
    const goHome = () => {
      router.push('/');
    };

    const fetchDataFrames = async () => {
      const id = route.params.id;
      // domain 已在初始化时确定，不再每次重置

      // 创建全屏加载动画
      const loadingInstance = ElLoading.service({
        lock: true,
        text: '正在获取数据，请稍候...',
        background: 'rgba(0, 0, 0, 0.7)'
      });

      try {
        const response = await axios.get(`http://localhost:8000/get_report_info/${domain.value || 'browser'}/${id}`, {
          params: { type: selectedType.value, scene: selectedScene.value }
        });
        dataFrames.value = response.data.dataframes;
        experiment.value = response.data.experiment;
        chinese_domain_name.value = experiment.value.chinese_domain_name;
        // 设置浏览器标签页标题为报告名
        if (experiment.value && experiment.value.name) {
          document.title = experiment.value.name;
        }

        // 成功提示
        ElMessage.success('数据获取成功');
      } catch (error) {
        console.error('Error fetching data frames:', error);
        // 失败提示
        ElMessage.error('数据获取失败，请稍后重试');
      } finally {
        // 关闭加载动画
        loadingInstance.close();
      }
    };

    const fetchBusinessInfo = async () => {
      try {
        const res = await axios.get(`http://localhost:8000/businesses/${domain.value}`);
        sceneOptions.value = Array.isArray(res.data.type) ? res.data.type : [];
        if (sceneOptions.value.length) {
          selectedScene.value = sceneOptions.value[0];
        }
      } catch (e) {
        console.error('获取业务场景失败');
      }
    };


    const arraySpanMethod = ({ row, rowIndex, columnIndex }, df) => {
      if (columnIndex === 0 && df.is_merged) {
        const currentGroup = row.组别;
        const previousRow = df.data[rowIndex - 1];

        if (previousRow && previousRow.组别 === currentGroup) {
          return [0, 0];
        } else {
          let rowspan = 1;
          for (let i = rowIndex + 1; i < df.data.length; i++) {
            if (df.data[i].组别 === currentGroup) {
              rowspan++;
            } else {
              break;
            }
          }
          return [rowspan, 1];
        }
      }
      return [1, 1];
    };

    // 导出 Excel 数据
    const exportToExcel = () => {
      // 假设 df 是你要导出的 DataFrame
      const wb = XLSX.utils.book_new();

      // 在导出前处理每个表的名称
      dataFrames.value.forEach((df) => {
        // 将工作表名称截断为不超过 31 个字符
        const sheetName = df.name.length > 31 ? df.name.slice(0, 31) : df.name;

        const ws = XLSX.utils.json_to_sheet(df.data);
        XLSX.utils.book_append_sheet(wb, ws, sheetName);
      });

      // 导出 Excel 文件
      XLSX.writeFile(wb, `${experiment.value.name}_report.xlsx`);
    };

    onMounted(() => {
      fetchBusinessInfo();
      fetchDataFrames();
    });

    return { selectedType, selectedScene, sceneOptions, dataFrames, fetchDataFrames, arraySpanMethod, experiment ,exportToExcel,goHome, domain, chinese_domain_name};
  }
};
</script>

<style scoped>
.custom-table {
  border: 2px solid black; /* 表格的整体外边框为黑色 */
  border-collapse: collapse; /* 合并单元格边框 */
}

.custom-table >>> .el-table__cell {
  border: 1px solid black !important; /* 单元格之间的边框为黑色 */
}

.custom-table .el-table__header-wrapper th {
  border: 1px solid black !important; /* 表头单元格的边框为黑色 */
}

.home-btn-container {
  margin-bottom: 20px;
  display: flex;
  justify-content: flex-start; /* 按钮居中对齐 */
}

.home-btn-container .el-button {
  font-size: 18px; /* 调整按钮字体大小 */
  font-weight: bold; /* 加粗字体 */
  padding: 12px 30px 12px 12px; /* 调整内边距，增加点击区域 */
  background-color: #409EFF; /* 主按钮色 */
  border-radius: 8px; /* 圆角 */
  color: white; /* 文字颜色 */
  box-shadow: 0 4px 10px rgba(64, 158, 255, 0.3); /* 添加阴影效果 */
  transition: transform 0.3s ease, box-shadow 0.3s ease; /* 动画效果 */
}

.home-btn-container .el-button:hover {
  background-color: #66b1ff; /* 更亮的颜色 */
  box-shadow: 0 6px 15px rgba(64, 158, 255, 0.5); /* 增强阴影 */
  transform: scale(1.05); /* 放大按钮 */
}




.table-divider {
  height: 4px; /* 增大分割线高度 */
  background-color: #409EFF; /* 改为蓝色，使分割线更显眼 */
  margin: 30px 0; /* 增加上下间距 */
}

.export-btn-container {
  margin-bottom: 20px;
  display: flex;
  justify-content: flex-start; /* 将按钮放置到最左边 */
}

.table-title {
  font-size: 20px;
  font-weight: bold;
  color: #333;
  margin: 20px 0;
  text-align: center;
}

.table-note {
  font-size: 0.9em; /* 调整字体大小，使其变小 */
  color: gray; /* 将字体颜色设置为灰色 */
  font-weight: normal; /* 取消标题的粗体效果 */
  margin: 5px 0; /* 设置适当的外边距 */
}

.title {
  text-align: center;
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 20px;
  color: black;
}

.experiment-info-card {
  margin-bottom: 20px;
  padding: 20px;
}

.info-row {
  margin-bottom: 10px;
  display: flex;
  justify-content: space-between;
}

.experiment-info p {
  font-size: 16px;
  margin: 5px 0;
  line-height: 1.5;
  color: #333;
}

.custom-table {
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.el-table th {
  background-color: #f3f6fd;
  font-weight: bold;
  color: #222;
  text-align: center;
  padding: 12px;
}

.el-table td, .el-table th {
  color: #333;
  padding: 10px;
}

.el-table tr:nth-child(even) td {
  background-color: #fafafa;
}

.el-table__row:hover td {
  background-color: #f5f5f5;
}

.type-select {
  width: 250px;
  font-size: 16px;
  color: #333;
  margin-bottom: 20px;
}

.type-select .el-input__inner {
  border-radius: 8px;
  border: 1px solid #dcdfe6;
  padding: 10px;
  transition: border-color 0.3s ease;
}

.type-select .el-input__inner:hover {
  border-color: #409EFF;
}

.type-select .el-input__inner:focus {
  border-color: #409EFF;
  box-shadow: 0 0 5px rgba(64, 158, 255, 0.5);
}
</style>
