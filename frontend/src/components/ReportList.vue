<template>
  <div>

    <!-- 顶部导航按钮 -->
    <div class="top-bar">
      <span class="top-bar-label">业务域：</span>
      <el-select
        v-model="currentDomain"
        @change="handleDomainChange"
        size="large"
        style="width: 260px;"
      >
        <el-option
          v-for="item in domainOptions"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
      <!-- 新增/编辑业务按钮 -->
      <el-button type="primary" size="large" @click="openBusinessDialog">新增业务</el-button>
      <el-button type="success" size="large" @click="openEditBusinessDialog">编辑当前业务</el-button>
    </div>

    <!-- 新增业务对话框 -->
    <el-dialog v-model="businessDialogVisible" title="业务配置" width="600px">
      <el-form :model="businessForm" label-width="120px">
        <el-form-item label="业务域 (英文)">
          <el-input v-model="businessForm.domain" placeholder="如 browser" :disabled="isEditBusiness"></el-input>
        </el-form-item>
        <el-form-item label="业务中文名称">
          <el-input v-model="businessForm.chinese_name" placeholder="浏览器"></el-input>
        </el-form-item>
        <el-form-item label="Dataflow API">
          <el-input v-model="businessForm.dataflow_api" placeholder="https://..."></el-input>
        </el-form-item>
        <el-form-item label="job_id列表">
          <div class="job-id-container">
            <div v-for="(jobId, index) in businessForm.job_ids_array" :key="index" class="job-id-item">
              <el-input v-model="businessForm.job_ids_array[index]" placeholder="输入job_id" style="width: 200px;"></el-input>
              <el-button type="danger" size="small" @click="removeJobId(index)" :disabled="businessForm.job_ids_array.length <= 1">-</el-button>
            </div>
            <el-button type="primary" size="small" @click="addJobId">+</el-button>
          </div>
        </el-form-item>
        
        <el-form-item label="指标名称配置">
          <div class="columns-config">
            <div v-for="jobId in businessForm.job_ids_array" :key="jobId" class="job-columns">
              <div class="job-header">
                <span class="job-title">[{{ jobId }}] :</span>
              </div>
              <div class="columns-list">
                <div class="column-item">
                  <el-input v-model="businessForm.columns_dict[jobId]" placeholder="指标名称" style="width: 150px;"></el-input>
                </div>
              </div>
            </div>
          </div>
        </el-form-item>
        
        <el-form-item label="指标中文名配置">
          <div class="columns-config">
            <div v-for="jobId in businessForm.job_ids_array" :key="jobId" class="job-columns">
              <div class="job-header">
                <span class="job-title">[{{ jobId }}] :</span>
              </div>
              <div class="columns-list">
                <div class="column-item">
                  <el-input v-model="businessForm.columns_cn_dict[jobId]" placeholder="指标中文名" style="width: 150px;"></el-input>
                </div>
              </div>
            </div>
          </div>
        </el-form-item>
        <el-form-item label="场景类型">
          <el-input v-model="businessForm.type_list" placeholder="env1,env2"></el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="businessDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitBusiness">提交</el-button>
      </template>
    </el-dialog>

    <h1>{{ domainTitle }}</h1>

    <!-- 配置按钮 -->
    <div style="text-align: right; margin-bottom: 20px;margin-right :30%">
      <el-button type="primary" @click="openConfigDialog">配置</el-button>
    </div>

    <div>
      <!-- 不再直接调用 fetchExperiments，而只是更新 searchQuery 的值 -->
      <el-input v-model="searchQuery" placeholder="搜索实验" style="width: 300px" ></el-input>
      <!-- 点击搜索按钮时调用 fetchExperiments 并且 page 设为 1 -->
      <el-button type="primary" @click="handleSearch">搜索</el-button>
    </div>

    <el-table :data="experiments" border style="width: 70%;margin: auto;margin-top: 20px">
    <el-table-column prop="id" label="ID" width="70"></el-table-column>
    <el-table-column prop="name" label="报告名称" width="120">
      <template #default="scope">
        <el-link @click="goToDetail(scope.row.id)" class="clickable-link">
          {{ scope.row.name }}
        </el-link>
      </template>
    </el-table-column>
    <el-table-column prop="creator" label="创建人" width="120"></el-table-column>
    <el-table-column prop="aa" label="AA实验周期" width="150"></el-table-column>
    <el-table-column prop="ab" label="AB实验周期" width="180"></el-table-column>
    <el-table-column label="报告状态" width="100">
      <template #default="scope">
        <el-tooltip :content="getStatusMeta(scope.row).label" placement="top">
          <el-icon :color="getStatusMeta(scope.row).color">
            <component :is="getStatusMeta(scope.row).icon" />
          </el-icon>
        </el-tooltip>
      </template>
    </el-table-column>
    <el-table-column prop="expId" label="实验ID" width="150"></el-table-column>
    <el-table-column prop="controlId" label="对照ID" width="150"></el-table-column>
    <el-table-column prop="userSize" label="用户规模" width="100"></el-table-column>
    <el-table-column label="操作">
      <template #default="scope">
        <el-button size="mini" type="primary" @click="openCopyDialog(scope.row)">复制</el-button>
        <el-button size="mini" type="primary" @click="openEditDialog(scope.row)">编辑</el-button>
        <el-button size="mini" type="danger" @click="handleDelete(scope.row.id,currentDomain)">删除</el-button>
      </template>
    </el-table-column>
  </el-table>


    <!-- 分页控件 -->
    <el-pagination
      background
      layout="total, sizes, prev, pager, next, jumper"
      :total="totalExperiments"
      :page-size="pageSize"
      :current-page="currentPage"
      :page-sizes="[10, 20, 50, 100]"
      @current-change="handlePageChange"
      @size-change="handleSizeChange"
      style="margin-top: 20px;">
    </el-pagination>

    <el-dialog title="配置实验报告" v-model="configDialogVisible" width="600px">
    <el-form :model="formData" label-width="100px" label-position="right" class="dialog-form">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="报告名称">
            <el-input v-model="formData.name"></el-input>
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="用户量级">
            <el-input v-model="formData.user_size"></el-input>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="实验组ID">
            <el-input v-model="formData.experiment_id"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="对照组ID">
            <el-input v-model="formData.control_id"></el-input>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="AA开始时间">
            <el-date-picker
              v-model="formData.AA_start"
              type="date"
              format="YYYYMMDD"
              value-format="YYYYMMDD"
            ></el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="AA结束时间">
            <el-date-picker
              v-model="formData.AA_end"
              type="date"
              format="YYYYMMDD"
              value-format="YYYYMMDD"
            ></el-date-picker>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">

        <el-col :span="12">
          <el-form-item label="AB开始时间">
            <el-date-picker
              v-model="formData.AB_start"
              type="date"
              format="YYYYMMDD"
              placeholder="选择开始日期"
              value-format="YYYYMMDD"
            ></el-date-picker>
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="AB结束时间">
            <el-date-picker
              v-model="formData.AB_end"
              type="date"
              format="YYYYMMDD"
              placeholder="选择结束日期"
              value-format="YYYYMMDD"
            ></el-date-picker>
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="备注">
        <el-input type="textarea" v-model="formData.notes" :rows="3"></el-input>
      </el-form-item>
    </el-form>

    <template #footer>
        <span class="dialog-footer">
          <el-button @click="configDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitConfig">提交</el-button>
        </span>
      </template>
    </el-dialog>


    <!-- 同步结果弹窗 -->
    <el-dialog
      v-model="resultDialogVisible"
      :title="resultTitle"
      width="400px"
      :close-on-click-modal="false"
      :show-close="true">
      <div>{{ resultMessage }}</div>
      <template #footer>
        <el-button type="primary" @click="resultDialogVisible = false">关闭</el-button>
      </template>
    </el-dialog>


  </div>
</template>

<script>
import axios from 'axios';
import { ref, onMounted, computed, watch } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { ElLoading,ElMessageBox, ElMessage } from 'element-plus';
import { Clock, Loading, CircleCheckFilled, QuestionFilled } from '@element-plus/icons-vue';

export default {
  setup() {
    const router = useRouter();
    const route = useRoute();
    // 旧版 Tab / 路由导航逻辑已废弃，故不再保留 activeTab 与 handleTabClick
    const experiments = ref([]);
    const searchQuery = ref('');
    const currentPage = ref(1);     // 当前页码
    const pageSize = ref(10);       // 每页显示的条数
    const totalExperiments = ref(0); // 总实验报告条数
    // 根据 URL 初始 domain
    const currentDomain = ref(route.query.domain || 'browser');

    // 监听地址栏 domain 变化（浏览器前进后退时保持同步）
    watch(
      () => route.query.domain,
      (newVal) => {
        if (newVal && newVal !== currentDomain.value) {
          currentDomain.value = newVal;
          currentPage.value = 1;
          fetchExperiments();
        }
      }
    );

    // 当下拉框改变 currentDomain 时，立即刷新实验列表
    watch(currentDomain, () => {
      currentPage.value = 1;
      fetchExperiments();
    });

    const domainOptions = ref([]);

    const fetchDomains = async () => {
      try {
        const res = await axios.get('http://localhost:8000/businesses');
        // res.data should be array of {domain:..., ...}
        domainOptions.value = res.data.map((biz) => ({
          value: biz.domain,
          label: biz.chinese_domain_name || biz.domain
        }));

        // 若当前 domain 不在返回列表，重设
        if (!domainOptions.value.find((o) => o.value === currentDomain.value) && domainOptions.value.length) {
          currentDomain.value = domainOptions.value[0].value;
        }
      } catch (err) {
        console.error('获取业务域失败', err);
      }
    };

    const domainTitle = computed(() => {
      const found = domainOptions.value.find((o) => o.value === currentDomain.value);
      const displayName = found ? found.label : currentDomain.value;
      return `${displayName}实验报告列表`;
    });

    const configDialogVisible = ref(false);  // 控制模态框显示/隐藏
    const formData = ref({
      name: '',
      experiment_id: '',
      control_id: '',
      AA_start: '',
      AA_end: '',
      AB_start: '',
      AB_end: '',
      user_size: '',
      notes: '',
    });
    const resultDialogVisible = ref(false); // 控制结果弹窗显示
    const resultTitle = ref(''); // 弹窗标题
    const resultMessage = ref(''); // 弹窗内容

    

    const parsePeriod = (period) => {
      if (!period) return ["", ""];
      const parts = period.split("-");
      return [parts[0] || "", parts[1] || ""];
    };

    const getTodayStr = () => {
      const d = new Date();
      const y = d.getFullYear();
      const m = String(d.getMonth() + 1).padStart(2, "0");
      const day = String(d.getDate()).padStart(2, "0");
      return `${y}${m}${day}`;
    };

    const getStatusMeta = (report) => {
      const today = getTodayStr();
      const [start, end] = parsePeriod(report.ab);
      if (!start || !end) return { label: "-", icon: QuestionFilled, color: "#909399" };
      if (today < start) return { label: "未开始", icon: Clock, color: "#909399" };
      if (today > end) return { label: "已结束", icon: CircleCheckFilled, color: "#67c23a" };
      return { label: "实验中", icon: Loading, color: "#e6a23c" };
    };

    const openEditDialog = (report) => {
      const [aaStart, aaEnd] = parsePeriod(report.aa);
      const [abStart, abEnd] = parsePeriod(report.ab);
      formData.value = {
        name: report.name,
        experiment_id: report.expId,
        control_id: report.controlId,
        AA_start: aaStart,
        AA_end: aaEnd,
        AB_start: abStart,
        AB_end: abEnd,
        user_size: report.userSize,
        notes: report.notes,
        id: report.id
      };
      configDialogVisible.value = true;
    };

    const openCopyDialog = (report) => {
      const [aaStart, aaEnd] = parsePeriod(report.aa);
      const [abStart, abEnd] = parsePeriod(report.ab);
      formData.value = {
        name: report.name,
        experiment_id: report.expId,
        control_id: report.controlId,
        AA_start: aaStart,
        AA_end: aaEnd,
        AB_start: abStart,
        AB_end: abEnd,
        user_size: report.userSize,
        notes: report.notes,
      };
      configDialogVisible.value = true;
    };

    // 获取实验报告列表
    const fetchExperiments = async (page = 1) => {
      const skip = (page - 1) * pageSize.value;
      try {
        const params = { skip, limit: pageSize.value, domain: currentDomain.value };
        if (searchQuery.value.trim()) params.query = searchQuery.value.trim();

        const response = await axios.get('http://localhost:8000/reports', { params });
        experiments.value = response.data.items;
        totalExperiments.value = response.data.total;
      } catch (error) {
        console.error('Error fetching reports:', error);
      }
    };

    // 当页面加载时获取实验列表
    onMounted(() => {
      fetchDomains();
      document.title = domainTitle.value;
      fetchExperiments(currentPage.value);
    });

    // 当 domainTitle 变化时同步更新浏览器标签页标题
    watch(domainTitle, (newTitle) => {
      document.title = newTitle;
    });

    // 处理分页的页码切换
    const handlePageChange = (page) => {
      currentPage.value = page;
      fetchExperiments(page);
    };

    const handleSizeChange = (size) => {
      pageSize.value = size;
      currentPage.value = 1;
      fetchExperiments();
    };

    // 处理搜索按钮的点击，重置到第一页
    const handleSearch = () => {
      currentPage.value = 1;
      fetchExperiments(currentPage.value);
    };

    const goToDetail = (id) => {
      // 使用 window.open 打开新的标签页或窗口
      const newWindow = window.open();
      // 构造目标页面的完整URL
      const url = router.resolve({ name: 'ReportInfo', params: { id }, query: { domain: currentDomain.value } }).href;
      newWindow.location.href = url;
    };

    // 打开配置表单模态框
    const openConfigDialog = () => {
      configDialogVisible.value = true;
    };

    const submitConfig = async () => {
      // 校验表单是否为空
      if (!formData.value.name.trim() || !formData.value.experiment_id.trim() ||
          !formData.value.control_id.trim() || !formData.value.AA_start ||
          !formData.value.AA_end || !formData.value.AB_start || !formData.value.AB_end ||
          !formData.value.user_size.trim()) {
        ElMessage.error('请完整填写所有字段');
        return;
      }

      const payload = {
        name: formData.value.name,
        domain: currentDomain.value,
        creator: 'wuweixin',
        aa: `${formData.value.AA_start}-${formData.value.AA_end}`,
        ab: `${formData.value.AB_start}-${formData.value.AB_end}`,
        expId: formData.value.experiment_id,
        controlId: formData.value.control_id,
        userSize: formData.value.user_size,
        notes: formData.value.notes
      };

      try {
        let response;
        if (formData.value.id) {
          // 更新
          response = await axios.put(`http://localhost:8000/reports/${formData.value.id}`, payload);
        } else {
          // 新增
          response = await axios.post('http://localhost:8000/reports', payload);
        }

        if (response.status === 200 || response.status === 201) {
          ElMessage.success(formData.value.id ? '更新成功' : '提交成功');
          configDialogVisible.value = false;
          fetchExperiments();
          resetFormData();
        } else {
          ElMessage.error('操作失败');
        }
      } catch (error) {
        ElMessage.error('操作失败：' + error.message);
      }
    };

    // 重置表单
    const resetFormData = () => {
      formData.value = {
        name: '',
        experiment_id: '',
        control_id: '',
        AA_start: '',
        AA_end: '',
        AB_start: '',
        AB_end: '',
        user_size: '',
        notes: '',
        id: '' // 清空 ID 字段
      };
    };


    // 同步数据
    const syncData = async () => {
      let loadingInstance; // Loading 实例
      try {
        // 显示加载中的遮罩
        loadingInstance = ElLoading.service({
          lock: true,
          text: '同步中，请稍候...',
          background: 'rgba(0, 0, 0, 0.7)',
        });

        // 发起同步请求
        const response = await axios.post('http://localhost:8000/sync_data');

        // 根据响应结果设置弹窗内容
        if (response.data.success) {
          resultTitle.value = '同步成功';
          resultMessage.value = '数据同步已成功完成！';
        } else {
          resultTitle.value = '同步失败';
          resultMessage.value = `数据同步失败：${response.data.message}`;
        }
      } catch (error) {
        // 处理异常
        resultTitle.value = '同步失败';
        resultMessage.value = `数据同步时发生错误：${error.message}`;
      } finally {
        // 无论成功还是失败，关闭加载遮罩并显示弹窗
        if (loadingInstance) {
          loadingInstance.close();
        }
        resultDialogVisible.value = true;
      }
    };

    const handleDelete = async (id) => {
      try {
        const confirmed = await ElMessageBox.confirm(
          '此操作将永久删除该实验报告，是否继续？',
          '警告',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
          }
        );

        if (confirmed) {
          const response = await axios.delete(`http://localhost:8000/reports/${id}?domain=${currentDomain.value}`);

          if (response.status === 200) {
            ElMessage.success('删除成功');
            fetchExperiments(currentPage.value);
          } else {
            ElMessage.error('删除失败');
          }
        }
      } catch (error) {
        if (error !== 'cancel') {
          console.error('Error deleting report:', error);
          ElMessage.error('删除操作失败');
        } else {
          ElMessage.info('删除已取消');
        }
      }
    };
    // 旧版 goToXXX 导航函数均已取消，统一使用 setDomain 切换业务域

    const setDomain = (domain) => {
      // 若 URL 中已是目标 domain，则不再更新
      if (domain === route.query.domain) return;
      // 更新地址栏，保留其他 query（如 page 等）
      router.replace({ query: { ...route.query, domain } });
    };

    const handleDomainChange = (val) => {
      setDomain(val);
    };

    // ─────────── 新增业务相关 ───────────
    const businessDialogVisible = ref(false);
    const isEditBusiness = ref(false);
    const businessForm = ref({
      domain: '',
      dataflow_api: '',
      job_ids_array: [''],
      columns_dict: {},
      columns_cn_dict: {},
      chinese_name: '',
      type_list: ''
    });

    const openBusinessDialog = () => {
      isEditBusiness.value = false;
      businessForm.value = {
        domain: '',
        dataflow_api: '',
        job_ids_array: [''],
        columns_dict: {},
        columns_cn_dict: {},
        chinese_name: '',
        type_list: ''
      };
      businessDialogVisible.value = true;
    };

    // 添加job_id
    const addJobId = () => {
      businessForm.value.job_ids_array.push('');
    };

    // 删除job_id
    const removeJobId = (index) => {
      if (businessForm.value.job_ids_array.length > 1) {
        const removedJobId = businessForm.value.job_ids_array[index];
        businessForm.value.job_ids_array.splice(index, 1);
        
        // 同时删除对应的指标配置
        if (businessForm.value.columns_dict[removedJobId]) {
          delete businessForm.value.columns_dict[removedJobId];
        }
        if (businessForm.value.columns_cn_dict[removedJobId]) {
          delete businessForm.value.columns_cn_dict[removedJobId];
        }
      }
    };

    // 添加指标
    const addColumn = (jobId, type) => {
      if (!jobId.trim()) {
        ElMessage.warning('请先输入job_id');
        return;
      }
      
      const targetDict = type === 'columns' ? businessForm.value.columns_dict : businessForm.value.columns_cn_dict;
      if (!targetDict[jobId]) {
        targetDict[jobId] = [];
      }
      targetDict[jobId].push('');
    };

    // 删除指标
    const removeColumn = (jobId, type, index) => {
      const targetDict = type === 'columns' ? businessForm.value.columns_dict : businessForm.value.columns_cn_dict;
      if (targetDict[jobId] && targetDict[jobId].length > 0) {
        targetDict[jobId].splice(index, 1);
      }
    };

    const submitBusiness = async () => {
      if (!businessForm.value.domain.trim()) {
        ElMessage.error('业务域不能为空');
        return;
      }
      
      // 验证job_ids不能为空
      const validJobIds = businessForm.value.job_ids_array.filter(id => id.trim());
      if (validJobIds.length === 0) {
        ElMessage.error('至少需要一个job_id');
        return;
      }
      
      try {
        // 清理数据：过滤空的job_id和对应的指标
        const cleanColumnsDict = {};
        const cleanColumnsCnDict = {};
        
        validJobIds.forEach(jobId => {
          if (businessForm.value.columns_dict[jobId]) {
            cleanColumnsDict[jobId] = businessForm.value.columns_dict[jobId].filter(col => col.trim());
          }
          if (businessForm.value.columns_cn_dict[jobId]) {
            cleanColumnsCnDict[jobId] = businessForm.value.columns_cn_dict[jobId].filter(col => col.trim());
          }
        });
        
        const payload = {
          domain: businessForm.value.domain.trim(),
          dataflow_api: businessForm.value.dataflow_api.trim(),
          job_id_dict: validJobIds,
          columns_dict: cleanColumnsDict,
          chinese_domain_name: businessForm.value.chinese_name.trim(),
          columns_chinese_dict: cleanColumnsCnDict,
          type: businessForm.value.type_list.split(',').map(s=>s.trim()).filter(Boolean)
        };
        
        if (isEditBusiness.value) {
          await axios.put(`http://localhost:8000/businesses/${businessForm.value.domain}`, payload);
        } else {
          await axios.post('http://localhost:8000/businesses', payload);
        }
        ElMessage.success(isEditBusiness.value ? '更新业务成功' : '新增业务成功');
        businessDialogVisible.value = false;
        isEditBusiness.value = false;
        fetchDomains();
      } catch (e) {
        ElMessage.error(`操作失败: ${e.response?.data?.detail || e.message}`);
      }
    };

    const openEditBusinessDialog = async () => {
      try {
        const res = await axios.get(`http://localhost:8000/businesses/${currentDomain.value}`);
        const biz = res.data;
        
        // 确保 job_ids_array 是数组格式
        const jobIdsArray = Array.isArray(biz.job_id_dict) ? biz.job_id_dict : Object.keys(biz.job_id_dict || {});
        
        businessForm.value = {
          domain: biz.domain,
          dataflow_api: biz.dataflow_api,
          job_ids_array: jobIdsArray.length > 0 ? jobIdsArray : [''],
          columns_dict: biz.columns_dict || {},
          columns_cn_dict: biz.columns_chinese_dict || {},
          chinese_name: biz.chinese_domain_name || '',
          type_list: Array.isArray(biz.type) ? biz.type.join(',') : ''
        };
        isEditBusiness.value = true;
        businessDialogVisible.value = true;
      } catch (e) {
        ElMessage.error('获取业务信息失败');
      }
    };


    return {
      experiments,
      searchQuery,
      currentPage,
      pageSize,
      totalExperiments,
      configDialogVisible, // 控制模态框
      formData,            // 表单数据
      fetchExperiments,
      handlePageChange,
      handleSizeChange,
      handleSearch,
      goToDetail,
      openConfigDialog,
      submitConfig,
      openEditDialog,
      getStatusMeta,
      syncData,
      resultDialogVisible,
      resultTitle,
      resultMessage,
      handleDelete,
      openCopyDialog,
      currentDomain,
      setDomain,
      domainTitle,
      domainOptions,
      handleDomainChange,
      businessDialogVisible,
      businessForm,
      openBusinessDialog,
      submitBusiness,
      openEditBusinessDialog,
      isEditBusiness,
      addJobId,
      removeJobId,
      addColumn,
      removeColumn
    };
  }
};
</script>

<style scoped>
.el-pagination {
  justify-content: center;
}

.dialog-form .el-form-item {
  margin-bottom: 15px;
}
.dialog-footer {
  text-align: right;
}
.el-input {
  width: 100%;
}
.clickable-link {
  color: #409eff; /* 蓝色以提示点击性 */
  text-decoration: underline; /* 添加下划线 */
}

.clickable-link:hover {
  color: #66b1ff; /* 悬停时的颜色 */
}

/* 顶部业务域选择区域放大 */
.top-bar {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  gap: 16px;
  font-size: 18px;
}

.top-bar-label {
  font-weight: bold;
}

.top-bar .el-select {
  font-size: 14px;
}

.top-bar .el-button {
  font-size: 14px;
  padding: 6px 12px;
}

/* 业务配置对话框样式 */
.job-id-container {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.job-id-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.columns-config {
  display: flex;
  flex-direction: column;
  gap: 16px;
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 12px;
}

.job-columns {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 8px;
  background-color: #fafafa;
}

.job-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8px;
  padding-bottom: 4px;
  border-bottom: 1px solid #e4e7ed;
}

.job-title {
  font-weight: bold;
  color: #606266;
}

.columns-list {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.column-item {
  display: flex;
  align-items: center;
  gap: 6px;
}
</style>
