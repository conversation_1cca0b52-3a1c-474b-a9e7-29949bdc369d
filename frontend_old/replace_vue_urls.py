import os


def replace_text_in_files(folder_path, old_text, new_text):
    # 遍历文件夹中的所有文件
    for root, _, files in os.walk(folder_path):
        for file in files:
            if file.endswith('.vue'):
                file_path = os.path.join(root, file)
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()

                # 替换文本
                updated_content = content.replace(old_text, new_text)

                # 写回文件
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(updated_content)
                print(f"已更新文件: {file_path}")


if __name__ == "__main__":
    # 指定文件夹路径
    folder_path = "src/components/"
    new_text = "http://localhost:8000/"
    old_text = "/api/"

    if os.path.isdir(folder_path):
        replace_text_in_files(folder_path, old_text, new_text)
        print("所有文件已处理完成！")
    else:
        print("输入的路径无效，请输入一个有效的文件夹路径。")
