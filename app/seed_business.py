import asyncio
import json

from sqlalchemy.ext.asyncio import AsyncSession

# 使用绝对导入，便于通过“python -m app.seed_business”或直接执行脚本
from database import AsyncSessionLocal
from models import BusinessConfig


async def seed_browser_business():
    """Insert default browser business configuration if not exists."""
    async with AsyncSessionLocal() as session:  # type: AsyncSession
        # Check exists
        exists = await session.get(BusinessConfig, "browser")
        if exists:
            print("[seed_business] browser domain already exists, updating it...")
            exists.dataflow_api = "https://api-dataflow.dt.mi.com/openapi/v1/service/ASUQOFFIHMNA_abtest_report"
            exists.job_id_dict = json.dumps(["browser_push", "browser_push_grouptype"])
            exists.columns_dict = json.dumps({
                "browser_push": [
                    "date", "job_id", "type", "exp_id",
                    "expose_pv", "expose_uv", "click_pv", "click_uv",
                    "pv_ctr", "uv_ctr",
                ],
                "browser_push_grouptype": [
                    "date", "job_id", "type", "exp_id", "grouptype",
                    "expose_pv", "expose_uv", "click_pv", "click_uv",
                    "pv_ctr", "uv_ctr",
                ],
            })
            exists.columns_chinese_dict = json.dumps({
                    "browser_push": [
                        "日期", "job_id", "type", "exp_id", "曝光pv", "曝光uv", "点击pv", "点击uv", "pv点击率", "uv点击率"
                    ],
                    "browser_push_grouptype": [
                        "日期", "job_id", "type", "exp_id", "用户分层", "曝光pv", "曝光uv", "点击pv", "点击uv", "pv点击率", "uv点击率"
                    ],
            })
            exists.chinese_domain_name = "浏览器"
            exists.type = json.dumps(["overall"])
            await session.commit()
            print("[seed_business] browser domain updated successfully.")
            return

        item = BusinessConfig(
            domain="browser",
            dataflow_api="https://api-dataflow.dt.mi.com/openapi/v1/service/ASUQOFFIHMNA_abtest_report",
            job_id_dict=json.dumps(["browser_push", "browser_push_grouptype"]),
            columns_dict=json.dumps(
                {
                    "browser_push": [
                        "date",
                        "job_id",
                        "type",
                        "exp_id",
                        "expose_pv",
                        "expose_uv",
                        "click_pv",
                        "click_uv",
                        "pv_ctr",
                        "uv_ctr",
                    ],
                    "browser_push_grouptype": [
                        "date",
                        "job_id",
                        "type",
                        "exp_id",
                        "grouptype",
                        "expose_pv",
                        "expose_uv",
                        "click_pv",
                        "click_uv",
                        "pv_ctr",
                        "uv_ctr",
                    ],
                }
            ),
            columns_chinese_dict=json.dumps(
                {
                    "browser" :{
                        "browser_push": ["日期", "job_id", "type", "exp_id", "曝光pv", "曝光uv", "点击pv", "点击uv","pv点击率","uv点击率"],
                        "browser_push_grouptype": ["日期", "job_id", "type", "exp_id","用户分层", "曝光pv", "曝光uv", "点击pv", "点击uv","pv点击率","uv点击率"]
                    }
                }
            ),
            chinese_domain_name="浏览器",
            type=json.dumps(["env1","env2","env3"]),
        )
        session.add(item)
        await session.commit()
        print("[seed_business] Inserted browser business configuration successfully.")


if __name__ == "__main__":
    asyncio.run(seed_browser_business()) 