# app/models.py
from sqlalchemy import Column, Integer, String, Index
from .database import Base, engine, getDb
from sqlalchemy import select, update, delete
from sqlalchemy.ext.asyncio import AsyncSession

class ReportList(Base):
    __tablename__ = "abtest_report_list"

    id = Column(Integer, primary_key=True)        # 不再 autoincrement
    domain = Column(String(255), nullable=False, index=True)
    Index("ux_domain_id", domain, id, unique=True)
    name = Column(String(255), nullable=False, comment="实验名称")
    aa = Column("AA", String(255), nullable=False, comment="AA 实验时间")
    ab = Column("AB", String(255), nullable=False, comment="AB 实验时间")
    expId = Column("exp_id", String(255), nullable=False, comment="实验组 ID")
    controlId = Column("control_id", String(255), nullable=False, comment="对照组 ID")
    userSize = Column("user_size", String(255), nullable=False, default="0", comment="用户量")
    creator = Column("creator", String(255), nullable=False, default="", comment="创建人")
    notes = Column(String(255), nullable=False, default="无", comment="备注")


# 动态业务配置表
class BusinessConfig(Base):
    """存储每个业务(域)的配置，包括 Dataflow API、job_id_dict、columns_dict 等。

    job_id_dict 和 columns_dict 均使用 JSON 字符串存储，读取时需要反序列化。"""

    __tablename__ = "abtest_business_config"

    # 业务域（浏览器、mivideo 等）作为主键，保证唯一
    domain = Column(String(255), primary_key=True, comment="业务域")

    # 小米 Dataflow 开放接口地址
    dataflow_api = Column(String(512), nullable=False, comment="Dataflow API")

    # JSON 格式的 job_id 列表，例如: ["browser_push", "browser_push_grouptype"]
    job_id_dict = Column(String, nullable=False, comment="JSON 序列化后的 job_id 列表")

    # JSON 格式的 columns 映射，例如: {"browser_push": [...], "browser_push_grouptype": [...]}。
    columns_dict = Column(String, nullable=False, comment="JSON 序列化后的 columns 映射")

    # JSON 格式的中文列名映射，可选
    columns_chinese_dict = Column(String, nullable=False, default="{}", comment="JSON 序列化后的中文列名映射")

    # 业务中文名称（显示给前端）
    chinese_domain_name = Column(String(255), nullable=False, default="", comment="业务中文名称")

    # 场景类型列表，JSON 字符串保存，例如: ["env1","env2"]
    type = Column(String, nullable=False, default="[]", comment="场景类型列表(JSON)")