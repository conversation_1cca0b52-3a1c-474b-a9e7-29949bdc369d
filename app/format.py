import pandas as pd
import numpy as np


def safe_percent(x):
    if pd.isna(x) or type(x) == str:
        return "-"
    return f"{x:.3f}%"

def safe_percent_2(x):
    if pd.isna(x) or type(x) == str:
        return "-"
    return f"{x*100:.3f}%"

def safe_float(x):
    if pd.isna(x) or type(x) == str:
        return "-"
    return f"{x:.3f}"

def safe_int_format(x):

    if pd.isna(x):
        return "-"
    try:
        return format(int(float(x)), ',')
    except Exception:
        return x

def df_formatter(df,report,job_id,columns_chinese_mapping):
    
     # 1. 对百分比列进行格式化
    percentage_index_rows = df[df['指标'].str.contains('ctr|utr|rate', case=False, na=False)].index
    selected_columns = ['AB实验均值', 'AB对照均值', 'AB平均增量', 'AB差值最大', 'AB差值最小', 'AA实验均值',
                          'AA对照均值', 'AA平均增量']
    df.loc[df['指标'].isin(percentage_index_rows), selected_columns] = df[selected_columns].applymap(
        safe_percent_2)

    # 2. 计算AB-AA
    df['AB-AA'] = df['AB平均增幅'] - df['AA平均增幅']
    
    df['AB-AA'] = df['AB-AA'].apply(safe_percent)
    df['AB平均增幅'] = df['AB平均增幅'].apply(safe_percent)
    df['AA平均增幅'] = df['AA平均增幅'].apply(safe_percent)
    df['AB_pvalue'] = df['AB_pvalue'].apply(safe_float)
    df['AA_pvalue'] = df['AA_pvalue'].apply(safe_float)

    # 3. 对整数列进行格式化, 对小数列进行格式化
    int_index_rows = df[df['指标'].str.contains('pv|uv|vv|time|ad_expose|ad_click', case=False, na=False)].index
    float_index_rows = df[df['指标'].str.contains('per|fee', case=False, na=False)].index
    int_index_rows = int_index_rows.difference(float_index_rows)
    float_index_rows = df.index.difference(int_index_rows.union(percentage_index_rows))

    df.loc[df['指标'].isin(int_index_rows), selected_columns] = df.loc[
        df['指标'].isin(int_index_rows), selected_columns].applymap(safe_int_format)
    df.loc[df['指标'].isin(float_index_rows), selected_columns] = df.loc[df['指标'].isin(float_index_rows), selected_columns].applymap(safe_float)
    
    # 4. 重命名指标
    df['指标'] = columns_chinese_mapping[job_id][4:]
    df.fillna("-", inplace=True)
    columns = list(df.columns)[:1] + ['AB-AA'] + list(df.columns)[1:-1]
    df = df[columns]

    return df

def _format_daily_df(df: 'pd.DataFrame',report,job_id,columns_chinese_mapping) -> 'pd.DataFrame':
    """每日明细报表统一格式化：列重命名、顺序调整、整数千分位。"""
    if "grouptype" in job_id:
        df.columns = ['组别'] + columns_chinese_mapping[job_id][4:]
    else:
        df.columns = ['组别', '日期'] + columns_chinese_mapping[job_id][4:]

    int_columns = [col for col in df.columns if any(keyword in col for keyword in ['uv', 'pv', 'vv','广告点击','广告曝光']) and '人均' not in col]
    # 安全整数格式化
    def safe_int_format(x):
        if pd.isna(x):
            return "-"
        try:
            return format(int(float(x)), ",")
        except Exception:
            return x

    # 安全百分比格式化
    def safe_percent_format(x):
        if pd.isna(x):
            return "-"
        try:
            return f"{float(x) * 100:.3f}%"
        except Exception:
            return x

    # 安全均值格式化
    def safe_avg_format(x):

        if pd.isna(x):
            return "-"
        try:
            return f"{float(x):.2f}"
        except Exception:
            return x
    
        # 对百分比列进行格式化
    
    percentage_columns = [col for col in df.columns if any(keyword in col for keyword in ['utr', 'ctr', '率'])]
    df.loc[df['组别'].isin(['实验组', '对照组', '绝对增长']), percentage_columns] = df.loc[
        df['组别'].isin(['实验组', '对照组', '绝对增长']), percentage_columns].applymap(safe_percent_format)


    # 对整数列进行格式化
    df.loc[df['组别'].isin(['实验组', '对照组', '绝对增长']), int_columns] = df.loc[
        df['组别'].isin(['实验组', '对照组', '绝对增长']), int_columns].applymap(safe_int_format)


    # 对人均vv和人均播时进行格式化
    avg_columns = [col for col in df.columns if '人均' in col or '收入' in col]
    df.loc[df['组别'].isin(['实验组', '对照组', '绝对增长']), avg_columns] = df.loc[
        df['组别'].isin(['实验组', '对照组', '绝对增长']), avg_columns].applymap(safe_avg_format)

    # 获取除了'组别'和'日期'之外的所有列
    columns_to_format = [col for col in df.columns if col not in ['组别', '日期']]

    # 对相对增长行的所有列进行百分比格式化
    def safe_relative_percent(x):
        if pd.isna(x):
            return "-"
        try:
            return f"{float(x):.3f}%"
        except Exception:
            return x

    df.loc[df['组别'] == '相对增长', columns_to_format] = df.loc[df['组别'] == '相对增长', columns_to_format].applymap(
        safe_relative_percent
    )
    df.fillna("-", inplace=True)

    return df

def df_formatter_2(df,report,job_id,columns_chinese_mapping):
    return _format_daily_df(df,report,job_id,columns_chinese_mapping)


def df_formatter_3(df,report,job_id,columns_chinese_mapping):
    return _format_daily_df(df,report,job_id,columns_chinese_mapping)

