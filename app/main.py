# app/main.py
import warnings
warnings.filterwarnings(
    "ignore",
    category=FutureWarning,
    module="app.format"      # 只屏蔽 format.py 内的 FutureWarning
)
from fastapi import FastAPI, HTTPException, Depends, Path, Query, Request
from sqlalchemy import select, update, delete, func
from sqlalchemy.ext.asyncio import AsyncSession
from .calculate import abtest_report_df
from typing import List
import time
import os
import httpx
import json
import pandas as pd

from .database import getDb, engine, Base
from .models import ReportList, BusinessConfig
from .schemas import (
    ReportCreate,
    ReportOut,
    ReportUpdate,
    ReportsPage,
    DataFrameSchema,
    ReportInfoOut,
    BusinessCreate,
    BusinessUpdate,
    BusinessOut,
)

app = FastAPI(title="ReportList CRUD API", version="1.0.0")

from fastapi.middleware.cors import CORSMiddleware

app.add_middleware(
    CORSMiddleware,
    allow_origins=[
        "http://localhost:5173",   # Vite 默认端口
        "http://localhost:8080",   # Vue-CLI/Webpack 默认端口
        "http://127.0.0.1:5173",
        "http://127.0.0.1:8080",
    ],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)
# ────────────────── 创建表（只需首次） ──────────────────
@app.on_event("startup")
async def onStartup():
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)

# ───────────────────── 中间件示例 ──────────────────────
@app.middleware("http")
async def addProcessTimeHeader(request: Request, call_next):
    start: float = time.time()
    response = await call_next(request)
    cost: float = time.time() - start
    response.headers["X-Process-Time"] = f"{cost:.6f}"
    return response
# ──────────────────────────────────────────────────────

async def next_id(db: AsyncSession, domain: str) -> int:
    result = await db.execute(
        select(func.coalesce(func.max(ReportList.id), 0)).where(ReportList.domain == domain)
    )
    return result.scalar_one() + 1

# 创建
@app.post("/reports", response_model=ReportOut, summary="创建 Report")
async def createReport(
    report: ReportCreate, db: AsyncSession = Depends(getDb)
):
    new_id = await next_id(db, report.domain)
    new_item = ReportList(id=new_id, **report.model_dump())
    db.add(new_item)
    await db.flush()          # 拿到自增 ID
    await db.refresh(new_item)
    return new_item

# 列表查询（分页）
@app.get("/reports", response_model=ReportsPage, summary="分页查询 Reports")
async def listReports(
    skip: int = Query(0, ge=0),
    limit: int = Query(10, ge=1, le=100),
    domain: str | None = Query(None, description="业务域/渠道"),
    query: str | None = Query(None, alias="query", description="搜索关键字 (按名称模糊匹配)"),
    db: AsyncSession = Depends(getDb),
):
    # 基础查询
    stmt = select(ReportList)
    if domain:
        stmt = stmt.where(ReportList.domain == domain)
    if query:
         stmt = stmt.where(ReportList.name.contains(query))

    # 总数
    total_result = await db.execute(select(func.count()).select_from(stmt.subquery()))
    total: int = total_result.scalar()

    # 分页
    stmt = stmt.offset(skip).limit(limit).order_by(ReportList.id.desc())
    result = await db.execute(stmt)
    reports: List[ReportOut] = list(map(lambda r: r[0], result.fetchall()))

    return {"total": total, "items": reports}

# 单条查询
@app.get("/reports/{reportId}", response_model=ReportOut, summary="按 ID 查询")
async def readReport(
    reportId: int = Path(..., ge=1),
    db: AsyncSession = Depends(getDb),
):
    res = await db.get(ReportList, reportId)
    if res is None:
        raise HTTPException(404, f"Report {reportId} 不存在")
    return res

# 更新
@app.put("/reports/{reportId}", response_model=ReportOut, summary="更新 Report")
async def updateReport(
    reportId: int = Path(..., ge=1),
    payload: ReportUpdate = ...,
    db: AsyncSession = Depends(getDb),
):
    # 提取需要更新的字段，并显式排除 domain 以避免触碰主键组合 (domain,id)
    update_fields = {
        k: v
        for k, v in payload.model_dump(exclude_none=True).items()
        if k != "domain"  # 不允许修改业务域
    }
    domain = update_fields.get("domain")
    if domain:
        stmt = select(ReportList).where(ReportList.domain == domain, ReportList.id == reportId)
        res = await db.execute(stmt)
        if res.scalar_one_or_none() is not None:
            raise HTTPException(409, f"业务 {domain} 已存在 id={reportId} 的报告，无法更新")

    if not update_fields:
        # 如果没有可更新字段，直接返回当前记录
        current = await db.get(ReportList, reportId)
        if current is None:
            raise HTTPException(404, f"Report {reportId} 不存在")
        return current

    stmt = (
        update(ReportList)
        .where(ReportList.id == reportId)
        .values(**update_fields)
    )
    res = await db.execute(stmt)
    if res.rowcount == 0:
        raise HTTPException(404, f"Report {reportId} 不存在")
    # 重新查询返回
    updated = await db.get(ReportList, reportId)
    return updated

# 删除
@app.delete("/reports/{reportId}", summary="删除 Report")
async def deleteReport(
    reportId: int = Path(..., ge=1),
    db: AsyncSession = Depends(getDb),
    domain: str = Query(..., description="业务域"),
):
    stmt = delete(ReportList).where(ReportList.id == reportId, ReportList.domain == domain)
    res = await db.execute(stmt)
    if res.rowcount == 0:
        raise HTTPException(404, f"Report {reportId} 不存在")
    return {"msg": f"Report {reportId} 已删除"}

# ────────────────── Report 详情 ──────────────────
@app.get("/get_report_info/{domain}/{reportId}", response_model=ReportInfoOut, summary="获取报告详情与数据帧")
async def getReportInfo(
    domain: str = Path(..., description="业务域"),
    reportId: int = Path(..., ge=1, description="报告ID"),
    type: str = Query("merged", description="merged / not_merged"),
    scene: str = Query("overall", description="overall / env1 / env2"),
    db: AsyncSession = Depends(getDb),
):
    # 查询实验基本信息（需同时匹配 domain）
    stmt = select(ReportList).where(ReportList.id == reportId, ReportList.domain == domain)
    res = await db.execute(stmt)
    report = res.scalar_one_or_none()
    if report is None:
        raise HTTPException(404, f"Report {reportId} 不存在于业务 {domain}")

    # 查询业务配置
    biz_conf = await db.get(BusinessConfig, report.domain)
    if biz_conf is None:
        raise HTTPException(404, f"业务配置 {report.domain} 不存在，请先创建")

    # 将字段转换为前端需要的格式
    expDict = {
        "name": report.name,
        "AA": report.aa,
        "AB": report.ab,
        "experiment_id": report.expId,
        "control_id": report.controlId,
        "user_size": report.userSize,
        "creator": report.creator,
        "notes": report.notes,
        "chinese_domain_name": biz_conf.chinese_domain_name,
    }

    # 调用业务计算函数，传入业务配置
    biz_conf_parsed = {
        "dataflow_api": biz_conf.dataflow_api,
        "job_id_dict": json.loads(biz_conf.job_id_dict),
        "columns_dict": json.loads(biz_conf.columns_dict),
        "columns_chinese_dict": json.loads(biz_conf.columns_chinese_dict),
    }

    dataframes = await abtest_report_df(report, biz_conf_parsed,type,scene)

    return {"experiment": expDict, "dataframes": dataframes}


# ────────────────── Business Config Endpoints ──────────────────

@app.post("/businesses", response_model=BusinessOut, summary="新增业务配置")
async def createBusinessConfig(payload: BusinessCreate, db: AsyncSession = Depends(getDb)):
    exists = await db.get(BusinessConfig, payload.domain)
    if exists:
        raise HTTPException(400, f"业务 {payload.domain} 已存在")

    item = BusinessConfig(
        domain=payload.domain,
        dataflow_api=payload.dataflow_api,
        job_id_dict=json.dumps(payload.job_id_dict),
        columns_dict=json.dumps(payload.columns_dict),
        columns_chinese_dict=json.dumps(payload.columns_chinese_dict or {}),
        chinese_domain_name=payload.chinese_domain_name or "",
        type=json.dumps(payload.type or []),
    )
    db.add(item)
    await db.flush()
    await db.refresh(item)

    return BusinessOut(
        domain=item.domain,
        dataflow_api=item.dataflow_api,
        job_id_dict=json.loads(item.job_id_dict),
        columns_dict=json.loads(item.columns_dict),
        columns_chinese_dict=json.loads(item.columns_chinese_dict or "{}"),
        chinese_domain_name=item.chinese_domain_name,
        type=json.loads(item.type or "[]"),
    )


@app.get("/businesses", response_model=list[BusinessOut], summary="获取业务配置列表")
async def listBusinessConfigs(db: AsyncSession = Depends(getDb)):
    result = await db.execute(select(BusinessConfig))
    items = result.scalars().all()
    
    return [
        BusinessOut(
            domain=i.domain,
            dataflow_api=i.dataflow_api,
            job_id_dict=json.loads(i.job_id_dict),
            columns_dict=json.loads(i.columns_dict),
            columns_chinese_dict=json.loads(i.columns_chinese_dict or "{}"),
            chinese_domain_name=i.chinese_domain_name,
            type=json.loads(i.type or "[]"),
        )
        for i in items
    ]


@app.get("/businesses/{domain}", response_model=BusinessOut, summary="获取单个业务配置")
async def getBusinessConfig(domain: str, db: AsyncSession = Depends(getDb)):
    item = await db.get(BusinessConfig, domain)
    
    if not item:
        raise HTTPException(404, f"业务 {domain} 不存在")
    return BusinessOut(
        domain=item.domain,
        dataflow_api=item.dataflow_api,
        job_id_dict=json.loads(item.job_id_dict),
        columns_dict=json.loads(item.columns_dict),
        columns_chinese_dict=json.loads(item.columns_chinese_dict or "{}"),
        chinese_domain_name=item.chinese_domain_name,
        type=json.loads(item.type or "[]"),
    )


@app.put("/businesses/{domain}", response_model=BusinessOut, summary="更新业务配置")
async def updateBusinessConfig(domain: str, payload: BusinessUpdate, db: AsyncSession = Depends(getDb)):
    item = await db.get(BusinessConfig, domain)
    if not item:
        raise HTTPException(404, f"业务 {domain} 不存在")

    # 数据验证和清理
    try:
        if payload.dataflow_api is not None:
            item.dataflow_api = payload.dataflow_api
        if payload.job_id_dict is not None:
            # 过滤掉空字符串
            if isinstance(payload.job_id_dict, list):
                clean_job_ids = [job_id for job_id in payload.job_id_dict if job_id and job_id.strip()]
                item.job_id_dict = json.dumps(clean_job_ids)
            else:
                item.job_id_dict = json.dumps(payload.job_id_dict)
        if payload.columns_dict is not None:
            # 清理空的 job_id 键
            clean_columns = {k: v for k, v in payload.columns_dict.items() if k and k.strip()}
            item.columns_dict = json.dumps(clean_columns)
        if payload.columns_chinese_dict is not None:
            # 清理空的 job_id 键
            clean_columns_cn = {k: v for k, v in payload.columns_chinese_dict.items() if k and k.strip()}
            item.columns_chinese_dict = json.dumps(clean_columns_cn)
        if payload.chinese_domain_name is not None:
            item.chinese_domain_name = payload.chinese_domain_name
        if payload.type is not None:
            # 过滤掉空字符串
            if isinstance(payload.type, list):
                clean_types = [t for t in payload.type if t and t.strip()]
                item.type = json.dumps(clean_types)
            else:
                item.type = json.dumps(payload.type)

        # 直接提交修改，不需要 merge
        await db.flush()
        await db.refresh(item)

        return BusinessOut(
            domain=item.domain,
            dataflow_api=item.dataflow_api,
            job_id_dict=json.loads(item.job_id_dict),
            columns_dict=json.loads(item.columns_dict),
            columns_chinese_dict=json.loads(item.columns_chinese_dict or "{}"),
            chinese_domain_name=item.chinese_domain_name,
            type=json.loads(item.type or "[]"),
        )
    except Exception as e:
        db.rollback()
        raise HTTPException(500, f"更新业务配置失败: {str(e)}")


@app.delete("/businesses/{domain}", summary="删除业务配置")
async def deleteBusinessConfig(domain: str, db: AsyncSession = Depends(getDb)):
    stmt = delete(BusinessConfig).where(BusinessConfig.domain == domain)
    res = await db.execute(stmt)
    if res.rowcount == 0:
        raise HTTPException(404, f"业务 {domain} 不存在")
    return {"msg": f"已删除业务 {domain}"}





