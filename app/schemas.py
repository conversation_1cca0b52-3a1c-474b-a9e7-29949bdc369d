# app/schemas.py
from pydantic import BaseModel, Field
from typing import Optional, List, Any
from .database import Base, engine, getDb
from sqlalchemy import select, update, delete
from sqlalchemy.ext.asyncio import AsyncSession

class ReportBase(BaseModel):
    name: str = Field(..., description="实验名称")
    domain: str = Field(..., description="业务名称")
    aa: str = Field(..., description="AA 实验时间")
    ab: str = Field(..., description="AB 实验时间")
    expId: str = Field(..., description="实验组 ID")
    controlId: str = Field(..., description="对照组 ID")
    userSize: str = Field("0", description="用户量")
    creator: str = Field("", description="创建人")
    notes: str = Field("无", description="备注")

class ReportCreate(ReportBase):  # 新增
    pass

class ReportUpdate(BaseModel):   # 修改（部分字段可选）
    name: Optional[str] = None
    domain: Optional[str] = None
    aa: Optional[str] = None
    ab: Optional[str] = None
    expId: Optional[str] = None
    controlId: Optional[str] = None
    userSize: Optional[str] = None
    creator: Optional[str] = None
    notes: Optional[str] = None

class ReportOut(ReportBase):     # 查询返回
    id: int = Field(..., ge=1)

    class Config:
        orm_mode = True

class ReportsPage(BaseModel):
    total: int
    items: List[ReportOut]

# 新增 DataFrame 与详情返回
class DataFrameSchema(BaseModel):
    name: str
    note: str | None = None
    columns: List[str]
    data: List[dict[str, Any]]
    is_merged: bool = False
    is_split: bool = False

class ReportInfoOut(BaseModel):
    experiment: dict[str, Any]
    dataframes: List[DataFrameSchema]


# ────────────────── BusinessConfig schemas ──────────────────

class BusinessBase(BaseModel):
    """公共字段基类"""
    domain: str = Field(..., description="业务域，如 browser / mivideo 等")
    dataflow_api: str = Field(..., description="Dataflow API 地址")
    job_id_dict: dict[str, list[str]] | list[str] = Field(..., description="job_id 列表或映射，支持 json")
    columns_dict: dict[str, list[str]] = Field(..., description="每个 job_id 对应的列名映射")
    columns_chinese_dict: dict[str, list[str]] | None = Field(default_factory=dict, description="中文列名映射，可选")
    chinese_domain_name: str | None = Field('', description="业务中文名称")
    type: list[str] | None = Field(default_factory=list, description="场景类型列表")


class BusinessCreate(BusinessBase):
    pass


class BusinessUpdate(BaseModel):
    dataflow_api: str | None = None
    job_id_dict: dict[str, list[str]] | list[str] | None = None
    columns_dict: dict[str, list[str]] | None = None
    columns_chinese_dict: dict[str, list[str]] | None = None
    chinese_domain_name: str | None = None
    type: list[str] | None = None


class BusinessOut(BusinessBase):
    class Config:
        orm_mode = True