# app/database.py
from typing import AsyncGenerator

from sqlalchemy.ext.asyncio import (
    create_async_engine,
    async_sessionmaker,
    AsyncSession,
)
from sqlalchemy.orm import declarative_base

# —— 请确认用户名、密码、主机、端口、库名均正确 ——
DATABASE_URL = (
    "mysql+aiomysql://"
    "abtest_push_wn:"
    "knzjuL_cee5p4FC6Wyu7ufyu-_SODZ08"
    "@gaea.test.mysql02.b2c.srv:13406/"
    "abtest_push"
    "?charset=utf8mb4"
)

# 1. 异步 Engine
engine = create_async_engine(
    DATABASE_URL,
    echo=False,          # 开发阶段可置 True 观察 SQL
    pool_pre_ping=True   # 避免连接池失效
)

# 2. 会话工厂
AsyncSessionLocal = async_sessionmaker(
    bind=engine, expire_on_commit=False, autoflush=False
)

# 3. Base，用于 ORM 映射
Base = declarative_base()

# 4. 依赖注入
async def getDb() -> AsyncGenerator[AsyncSession, None]:
    async with AsyncSessionLocal() as session:
        try:
            yield session
            await session.commit()
        except Exception:
            await session.rollback()
            raise