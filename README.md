# FastAPI 项目启动指南

本文档说明如何在本地环境下安装依赖并启动 FastAPI 服务。

## 前置条件

1. 已安装 [Python 3.10+](https://www.python.org/)。
2. 建议使用虚拟环境（`venv` 或 `conda`）。

## 安装依赖

```powershell
# Windows PowerShell
python -m venv venv
venv\Scripts\activate
pip install -r requirements.txt
```

## 启动服务

```powershell
uvicorn app.main:app --reload
```

- 访问 `http://127.0.0.1:8000/docs` 查看自动生成的 Swagger UI。
- 访问 `http://127.0.0.1:8000/redoc` 查看 ReDoc 文档。

## 常见问题

- 如遇到端口占用，请指定其他端口，例如 `--port 8080`。
- 如果修改代码后界面未自动刷新，确认是否已添加 `--reload` 参数。 